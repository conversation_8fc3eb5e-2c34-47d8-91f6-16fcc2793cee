#!/usr/bin/env python3
"""
Script to investigate why video 163 doesn't have a recipe
"""
import sys
import os
sys.path.append('.')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.database import Video, Recipe, ProcessingJob
from utils.recipe_extractor import RecipeExtractor

# Database configuration
DATABASE_URL = "sqlite:///db/tagTok.db"

def investigate_video_163():
    """Investigate video 163 recipe extraction"""
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Get video 163
        video = db.query(Video).filter(Video.id == 163).first()
        
        if not video:
            print("❌ Video 163 not found!")
            return
        
        print(f"📹 Video 163 Details:")
        print(f"  Title: {video.title}")
        print(f"  Original filename: {video.original_filename}")
        print(f"  Processing status: {video.processing_status}")
        print(f"  Processed: {video.processed}")
        print(f"  Processing progress: {video.processing_progress}%")
        print(f"  Upload date: {video.upload_date}")
        print(f"  Duration: {video.duration} seconds")
        
        # Check if transcript exists
        if video.transcript:
            print(f"  Transcript length: {len(video.transcript)} characters")
            print(f"  Transcript language: {video.transcript_language}")
            print(f"  Transcript preview: {video.transcript[:200]}...")
        else:
            print("  ❌ No transcript available")
        
        # Check if recipe exists
        recipe = db.query(Recipe).filter(Recipe.video_id == 163).first()
        if recipe:
            print(f"  ✅ Recipe found:")
            print(f"    Title: {recipe.title}")
            print(f"    Confidence: {recipe.extraction_confidence}")
            print(f"    Extracted at: {recipe.extracted_at}")
        else:
            print("  ❌ No recipe found")
        
        # Check processing jobs
        jobs = db.query(ProcessingJob).filter(ProcessingJob.video_id == 163).all()
        print(f"  Processing jobs: {len(jobs)}")
        for job in jobs:
            print(f"    Job {job.id}: {job.job_type} - {job.status}")
            if job.error_message:
                print(f"      Error: {job.error_message}")
        
        # Test recipe extraction logic if transcript exists
        if video.transcript:
            print(f"\n🧪 Testing Recipe Extraction Logic:")
            
            # Initialize recipe extractor
            extractor = RecipeExtractor()
            
            # Test if it's considered a cooking video
            is_cooking = extractor._is_cooking_video(video.transcript, video.title or video.original_filename)
            print(f"  Is cooking video: {is_cooking}")
            
            if is_cooking:
                print("  ✅ Video is classified as cooking content")
                print("  🔍 Checking why recipe extraction might have failed...")
                
                # Check for cooking keywords
                cooking_keywords = [
                    'recipe', 'cook', 'bake', 'fry', 'boil', 'grill', 'roast', 'sauté',
                    'ingredient', 'flour', 'sugar', 'salt', 'pepper', 'oil', 'butter',
                    'onion', 'garlic', 'tomato', 'chicken', 'beef', 'pork', 'fish',
                    'minute', 'hour', 'temperature', 'oven', 'pan', 'pot', 'bowl',
                    'mix', 'stir', 'add', 'pour', 'slice', 'chop', 'dice', 'cut',
                    'serve', 'dish', 'meal', 'food', 'kitchen', 'cooking'
                ]
                
                text = f"{video.title or ''} {video.transcript}".lower()
                found_keywords = [keyword for keyword in cooking_keywords if keyword in text]
                print(f"  Found cooking keywords ({len(found_keywords)}): {found_keywords[:10]}...")
                
            else:
                print("  ❌ Video is NOT classified as cooking content")
                print("  This is why no recipe was extracted")
        
        print(f"\n📊 Summary:")
        if not video.transcript:
            print("  🔴 No transcript = No recipe extraction possible")
        elif not recipe:
            print("  🟡 Transcript exists but no recipe extracted")
            print("  Possible reasons:")
            print("    - Video not classified as cooking content")
            print("    - Ollama extraction failed")
            print("    - Low confidence score (< 0.5)")
            print("    - Processing error")
        else:
            print("  🟢 Recipe exists and was extracted successfully")
        
    except Exception as e:
        print(f"❌ Error investigating video 163: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    investigate_video_163()
